import { cn } from '@cloc/ui';
import type { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';

function ClocTimerFooter({ className }: { className?: string }): ReactElement {
	const { t } = useTranslation();
	return (
		<div
			className={cn(
				'flex justify-center items-center dark:text-white text-black text-[0.6rem] font-normal',
				className
			)}
		>
			{t('FOOTER.powered_by')}{' '}
			<a className=" font-bold underline" href="https://cloc.ai/" target="_blank" rel="noopener noreferrer">
				{t('ORGANIZATION')} {t('TITLE')}
			</a>
		</div>
	);
}

export { ClocTimerFooter };
