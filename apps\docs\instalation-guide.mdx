# Getting Started with Cloc SDK

Welcome to Cloc SDK! This comprehensive guide will help you integrate powerful time tracking and analytics capabilities into your application. Whether you're building a productivity tool, project management system, or team collaboration platform, Cloc SDK provides the building blocks you need.

## Prerequisites

- An Ever Cloc account, which you can create [here](https://app.cloc.ai)
- Basic familiarity with React and TypeScript
- Understanding of React Context and Providers

## Environment Setup

### Configure Environment Variables

Create a `.env.local` file in your project root:

```
# Cloc API URL
CLOC_API_URL=https://api.cloc.ai/api
```

### Install SDK Packages

Install the core Cloc SDK packages and their peer dependencies:

<Tabs>

<TabItem value="npm" label="npm" >
```bash
# Core SDK packages
npm install @cloc/atoms
```
</TabItem>

<TabItem value="pnpm" label="pnpm" >
```bash
# Core SDK packages
pnpm add @cloc/atoms
```
</TabItem>

<TabItem value="yarn" label="yarn" >
```bash
# Core SDK packages
yarn add  @cloc/atoms
```
</TabItem>

</Tabs>

Above installation will auto-install all required Cloc packages and peer dependencies.

### Tailwind CSS Configuration

The Cloc SDK packages preserve raw Tailwind CSS classes in their build output. Your application must process these classes through its own Tailwind build pipeline.

#### Configure Tailwind Content Paths

Update your `tailwind.config.js` to include Cloc package paths:

<Tabs>
  <TabItem value="tailwind-v3" label="Tailwind CSS v3" default>

  ```js
  /** @type {import('tailwindcss').Config} */
  module.exports = {
    content: [
      // Your app content
      "./src/**/*.{js,ts,jsx,tsx}",
      "./app/**/*.{js,ts,jsx,tsx}",
      "./pages/**/*.{js,ts,jsx,tsx}",
      "./components/**/*.{js,ts,jsx,tsx}",

      // Cloc SDK packages
      "./node_modules/@cloc/atoms/dist/**/*.js",
      "./node_modules/@cloc/ui/dist/**/*.js",
    ],
    darkMode: ['class'],
    theme: {
      extend: {
        colors: {
          // Required CSS variables for Cloc components
          border: "hsl(var(--border))",
          input: "hsl(var(--input))",
          ring: "hsl(var(--ring))",
          background: "hsl(var(--background))",
          foreground: "hsl(var(--foreground))",
          primary: {
            DEFAULT: "hsl(var(--primary))",
            foreground: "hsl(var(--primary-foreground))",
          },
          secondary: {
            DEFAULT: "hsl(var(--secondary))",
            foreground: "hsl(var(--secondary-foreground))",
          },
          destructive: {
            DEFAULT: "hsl(var(--destructive))",
            foreground: "hsl(var(--destructive-foreground))",
          },
          muted: {
            DEFAULT: "hsl(var(--muted))",
            foreground: "hsl(var(--muted-foreground))",
          },
          accent: {
            DEFAULT: "hsl(var(--accent))",
            foreground: "hsl(var(--accent-foreground))",
          },
          popover: {
            DEFAULT: "hsl(var(--popover))",
            foreground: "hsl(var(--popover-foreground))",
          },
          card: {
            DEFAULT: "hsl(var(--card))",
            foreground: "hsl(var(--card-foreground))",
          },
          // Custom Cloc colors
          primaryColor: "#3826A6",
          secondaryColor: "#A11DB1",
        },
        borderRadius: {
          lg: "var(--radius)",
          md: "calc(var(--radius) - 2px)",
          sm: "calc(var(--radius) - 4px)",
        },
      },
    },
    plugins: [require("tailwindcss-animate")],
  }
  ```

  </TabItem>
  <TabItem value="tailwind-v4" label="Tailwind CSS v4">

  ```js
  /** @type {import('tailwindcss').Config} */
  export default {
    content: [
      // Your app content
      "./src/**/*.{js,ts,jsx,tsx}",
      "./app/**/*.{js,ts,jsx,tsx}",
      "./pages/**/*.{js,ts,jsx,tsx}",
      "./components/**/*.{js,ts,jsx,tsx}",

      // Cloc SDK packages
      "./node_modules/@cloc/atoms/dist/**/*.js",
      "./node_modules/@cloc/ui/dist/**/*.js",
    ],
    theme: {
      extend: {
        colors: {
          // Required CSS variables for Cloc components
          border: "hsl(var(--border))",
          input: "hsl(var(--input))",
          ring: "hsl(var(--ring))",
          background: "hsl(var(--background))",
          foreground: "hsl(var(--foreground))",
          primary: {
            DEFAULT: "hsl(var(--primary))",
            foreground: "hsl(var(--primary-foreground))",
          },
          secondary: {
            DEFAULT: "hsl(var(--secondary))",
            foreground: "hsl(var(--secondary-foreground))",
          },
          destructive: {
            DEFAULT: "hsl(var(--destructive))",
            foreground: "hsl(var(--destructive-foreground))",
          },
          muted: {
            DEFAULT: "hsl(var(--muted))",
            foreground: "hsl(var(--muted-foreground))",
          },
          accent: {
            DEFAULT: "hsl(var(--accent))",
            foreground: "hsl(var(--accent-foreground))",
          },
          popover: {
            DEFAULT: "hsl(var(--popover))",
            foreground: "hsl(var(--popover-foreground))",
          },
          card: {
            DEFAULT: "hsl(var(--card))",
            foreground: "hsl(var(--card-foreground))",
          },
          // Custom Cloc colors
          primaryColor: "#3826A6",
          secondaryColor: "#A11DB1",
        },
        borderRadius: {
          lg: "var(--radius)",
          md: "calc(var(--radius) - 2px)",
          sm: "calc(var(--radius) - 4px)",
        },
      },
    },
    plugins: ["@tailwindcss/typography", "tailwindcss-animate"],
  }
  ```

  </TabItem>
</Tabs>

#### Add Required CSS Variables

Add these CSS variables to your global CSS file (e.g., `globals.css`):

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;
    --ring: 215 20.2% 65.1%;
    --radius: 0.5rem;

    /* Custom Cloc variables */
    --primaryColor: #3826A6;
    --secondaryColor: #A11DB1;
    --mainColor: linear-gradient(135deg, #3826A6 0%, #A11DB1 100%);
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;
    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;
    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;
    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;
    --border: 216 34% 17%;
    --input: 216 34% 17%;
    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;
    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
    --ring: 216 34% 17%;
    --radius: 0.5rem;
  }
}
```

:::info Package Processing
The Cloc SDK packages export raw Tailwind classes that must be processed by your application's Tailwind build pipeline. Ensure the package paths are included in your Tailwind content configuration.
:::

## Framework-Specific Setup

<Tabs>
<TabItem value="next-app-router" label="Next.js App Router" default>

#### 1. Create Root Layout with Providers

Create or update your root layout (`src/app/layout.tsx`):

```tsx
import { ClocProvider } from "@cloc/atoms";
import "./globals.css";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <ClocProvider
          config={{
            apiUrl: process.env.NEXT_PUBLIC_CLOC_API_URL,
          }}
        >
          {children}
        </ClocProvider>
      </body>
    </html>
  );
}
```

#### 2. Update `next.config.js`

Configure Next.js to transpile Cloc packages:

```js
/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ["@cloc/atoms", "@cloc/ui", "@cloc/api", "@cloc/types", "@cloc/tracking"],
  webpack: (config) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
    };
    return config;
  },
};

module.exports = nextConfig;
```

#### 3. Configure Tailwind CSS

Create or update `tailwind.config.js`:

```js
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    "./app/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@cloc/atoms/dist/**/*.js",
    "./node_modules/@cloc/ui/dist/**/*.js",
  ],
  darkMode: ['class'],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        primaryColor: "#3826A6",
        secondaryColor: "#A11DB1",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
```

</TabItem>
<TabItem value="next-pages-router" label="Next.js Pages Router">

#### 1. Update \_app.tsx

```tsx
import type { AppProps } from "next/app";
import { ClocProvider } from "@cloc/atoms";
import "../styles/globals.css";

export default function App({ Component, pageProps }: AppProps) {
  return (
    <ClocProvider
      config={{
        apiUrl: process.env.NEXT_PUBLIC_CLOC_API_URL,
      }}
    >
      <Component {...pageProps} />
    </ClocProvider>
  );
}
```

#### 2. Update `next.config.js`

```js
/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ["@cloc/atoms", "@cloc/ui", "@cloc/api", "@cloc/types", "@cloc/tracking"],
  webpack: (config) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
    };
    return config;
  },
};

module.exports = nextConfig;
```

#### 3. Configure Tailwind CSS

Create or update `tailwind.config.js`:

```js
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@cloc/atoms/dist/**/*.js",
    "./node_modules/@cloc/ui/dist/**/*.js",
  ],
  darkMode: ['class'],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        primaryColor: "#3826A6",
        secondaryColor: "#A11DB1",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
```

</TabItem>
<TabItem value="remix" label="Remix">

#### 1. Update root.tsx

```tsx
import {
  Links,
  LiveReload,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from "@remix-run/react";
import { ClocProvider } from "@cloc/atoms";

export default function App() {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        <ClocProvider
          config={{
            apiUrl: process.env.CLOC_API_URL,
          }}
        >
          <Outlet />
        </ClocProvider>
        <ScrollRestoration />
        <Scripts />
        <LiveReload />
      </body>
    </html>
  );
}
```

#### 2. Update `vite.config.ts` (Remix Vite)

```ts
import { vitePlugin as remix } from "@remix-run/dev";
import { defineConfig } from "vite";

export default defineConfig({
  plugins: [remix()],
  optimizeDeps: {
    include: ["@cloc/atoms", "@cloc/ui", "@cloc/api", "@cloc/types", "@cloc/tracking"],
  },
  ssr: {
    noExternal: ["@cloc/atoms", "@cloc/ui", "@cloc/api", "@cloc/types", "@cloc/tracking"],
  },
});
```

#### 3. Configure Tailwind CSS

Create or update `tailwind.config.ts`:

```ts
import type { Config } from "tailwindcss";

export default {
  content: [
    "./app/**/{**,.client,.server}/**/*.{js,jsx,ts,tsx}",
    "./node_modules/@cloc/atoms/dist/**/*.js",
    "./node_modules/@cloc/ui/dist/**/*.js",
  ],
  darkMode: ['class'],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        primaryColor: "#3826A6",
        secondaryColor: "#A11DB1",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;
```

</TabItem>
<TabItem value="vite" label="Vite">

#### 1. Update main.tsx

```tsx
import React from "react";
import ReactDOM from "react-dom/client";
import { ClocProvider } from "@cloc/atoms";
import App from "./App";
import "./index.css";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ClocProvider
      config={{
        apiUrl: import.meta.env.VITE_CLOC_API_URL,
      }}
    >
      <App />
    </ClocProvider>
  </React.StrictMode>
);
```

#### 2. Update `vite.config.ts`

```ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    include: ["@cloc/atoms", "@cloc/ui", "@cloc/api", "@cloc/types", "@cloc/tracking"],
  },
  define: {
    global: "globalThis",
  },
});
```

#### 3. Configure Tailwind CSS

Create or update `tailwind.config.js`:

```js
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@cloc/atoms/dist/**/*.js",
    "./node_modules/@cloc/ui/dist/**/*.js",
  ],
  darkMode: ['class'],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        primaryColor: "#3826A6",
        secondaryColor: "#A11DB1",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
```

</TabItem>
</Tabs>
