# Getting Started with Cloc SDK

Welcome to Cloc SDK! This comprehensive guide will help you integrate powerful time tracking and analytics capabilities into your application. Whether you're building a productivity tool, project management system, or team collaboration platform, Cloc SDK provides the building blocks you need.

## Prerequisites

- An Ever Cloc account, which you can create [here](https://app.cloc.ai)
- Basic familiarity with React and TypeScript
- Understanding of React Context and Providers

## Environment Setup

### Configure Environment Variables

Create a `.env.local` file in your project root:

```
# Cloc API URL
CLOC_API_URL=https://api.cloc.ai/api
```

### Install SDK Packages

Install the core Cloc SDK packages and their peer dependencies:

<Tabs>

<TabItem value="npm" label="npm" >
```bash
# Core SDK packages
npm install @cloc/atoms
```
</TabItem>

<TabItem value="pnpm" label="pnpm" >
```bash
# Core SDK packages
pnpm add @cloc/atoms
```
</TabItem>

<TabItem value="yarn" label="yarn" >
```bash
# Core SDK packages
yarn add  @cloc/atoms
```
</TabItem>

</Tabs>

Above installation will auto-install all required Cloc packages and peer dependencies.

### CSS Imports

Import the required stylesheets in your application either in your main root component or global CSS file:

<Tabs>
  <TabItem value="js" label="JS/TS Import" default>
  
  ```tsx
  // In your main root component (e.g. App.tsx)
  import "@cloc/atoms/styles.css";
  import "@cloc/ui/styles.css";
  ```
  
  </TabItem>
  <TabItem value="css" label="CSS Import">
  
  ```css
  /* In your main CSS file (e.g. global.css) */
  @import "@cloc/atoms/styles.css";
  @import "@cloc/ui/styles.css";
  ```
  
  </TabItem>
</Tabs>

:::info Stylesheet Order
Import @cloc/atoms and @cloc/ui styles before your custom styles to ensure proper CSS cascade and customization capabilities.
:::

## Framework-Specific Setup

<Tabs>
<TabItem value="next-app-router" label="Next.js App Router" default>

#### 1. Create Root Layout with Providers

Create or update your root layout (`src/app/layout.tsx`):

```tsx
import { ClocProvider } from "@cloc/atoms";
import "./globals.css";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <ClocProvider
          config={{
            apiUrl: process.env.NEXT_PUBLIC_CLOC_API_URL,
          }}
        >
          {children}
        </ClocProvider>
      </body>
    </html>
  );
}
```

#### 2. Update `next.config.js`

Update your `next.config.js` to include Cloc components:

```js
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  transpilePackages: ["@cloc/atoms", "@cloc/ui", "@cloc/api", "@cloc/types"],
  webpack: (config) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
    };
    return config;
  },
};

module.exports = nextConfig;
```

</TabItem>
<TabItem value="next-pages-router" label="Next.js Pages Router">

#### 1. Update \_app.tsx

```tsx
import type { AppProps } from "next/app";
import { ClocProvider } from "@cloc/atoms";
import "../styles/globals.css";

export default function App({ Component, pageProps }: AppProps) {
  return (
    <ClocProvider
      config={{
        apiUrl: process.env.NEXT_PUBLIC_CLOC_API_URL,
      }}
    >
      <Component {...pageProps} />
    </ClocProvider>
  );
}
```

</TabItem>
<TabItem value="remix" label="Remix">

#### 1. Update root.tsx

```tsx
import {
  Links,
  LiveReload,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
} from "@remix-run/react";
import { ClocProvider } from "@cloc/atoms";

export default function App() {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        <ClocProvider
          config={{
            apiUrl: process.env.CLOC_API_URL,
          }}
        >
          <Outlet />
        </ClocProvider>
        <ScrollRestoration />
        <Scripts />
        <LiveReload />
      </body>
    </html>
  );
}
```

#### 2. Update remix.config.js

```js
/** @type {import('@remix-run/dev').AppConfig} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@cloc/atoms/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@cloc/ui/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: "var(--cloc-primary)",
        secondary: "var(--cloc-secondary)",
        accent: "var(--cloc-accent)",
        background: "var(--cloc-background)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
```

</TabItem>
<TabItem value="vite" label="Vite">

#### 1. Update main.tsx

```tsx
import React from "react";
import ReactDOM from "react-dom/client";
import { ClocProvider } from "@cloc/atoms";
import App from "./App";
import "./index.css";

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ClocProvider
      config={{
        apiUrl: import.meta.env.VITE_CLOC_API_URL,
      }}
    >
      <App />
    </ClocProvider>
  </React.StrictMode>
);
```

#### 2. Update vite.config.ts

```ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    include: ["@cloc/atoms", "@cloc/ui", "@cloc/api", "@cloc/types"],
  },
  define: {
    global: "globalThis",
  },
});
```

</TabItem>
</Tabs>
